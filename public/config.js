// 环境配置
window.CONFIG = {
  // 开发环境配置
  development: {
    baseURL: '/api', // 使用本地代理
    timeout: 1000 * 60 * 10,
    uploadURL: 'http://localhost:3000/upload',
    websocketURL: 'ws://localhost:3001',
    // 功能开关
    debug: true,
    mock: true,
    enablePWA: false,
    enableAnalytics: false,
    logLevel: 'debug'
  },

  // 生产环境配置
  production: {
    baseURL: '/api',
    timeout: 10000,
    uploadURL: '/api/upload',
    websocketURL: 'wss://your-domain.com/ws',
    // 功能开关
    debug: false,
    mock: false,
    enablePWA: true,
    enableAnalytics: true,
    logLevel: 'error'
  },

  // 测试环境配置
  test: {
    baseURL: 'http://test-api.example.com/api',
    timeout: 10000,
    uploadURL: 'http://test-api.example.com/upload',
    websocketURL: 'ws://test-api.example.com:3001',
    // 功能开关
    debug: true,
    mock: false,
    enablePWA: false,
    enableAnalytics: false,
    logLevel: 'info'
  }
}

// 获取当前环境配置
window.getConfig = function () {
  // 从HTML的data-env属性获取环境信息
  const env = document.documentElement.getAttribute('data-env') || 'development'
  return window.CONFIG[env] || window.CONFIG.development
}
window.getProjectId = function () {
  return '832a5bf2-fa7e-497b-b849-090c16c02b52'
}
window.getToken = function () {
  return 'D5318751F71E499BB6D6495D88DBEF27D22C22E9A9F57E3BDDD9153C5AE8D377725C77EC9AE20AD9043FFCCE519CB3BBCACB7D6D319ACF5FF6E1E8DB2006D4692776E1C90553AEAA68D969846B3B0A26'
}
window.getSceneUrl = function () {
  return '../../public/scenemanager/#/'
}

