// 发布环境配置
(function () {
  window.IP_CONFIG = {
    BASE_URL: '//dev-server.vothing.com', // 基础api baseUrl
    WEBSOCKET_URL: '//demo-server.vothing.com/vothing', // ws baseUrl
    SOURCES_URL: '//multiverse.vothing.com/',// multiverse.js所在的路径,用于获取静态资源文件
    MODEL_URL: '//multiverse-server.vothing.com', // 模型服务 数据请求地址
    DGCC_URL: '//demo-server.vothing.com/api/Material/GetBaidu', // 地图搜索服务地址，这个vothing提供的
    // DGCC_URL: "//************:5000/api/v1/scene/GetBaiduMapLocation", // 地图搜索服务地址，这个bime提供的
    PANO_URL: '//************:9960', // 全景图
    PROJECT_ID: '',//项目id(可选，也可以通过url传递该参数，参考在线示例"初始化-引入功能UI")
    MapboxToken: 'YOUR_MAPBOX_ACCESS_TOKEN'
  }
  window.PROJECT_TITLE = '场景设计器' // 项目名
  window.LBS_ENABLED = true //搜索定位功能是否启用，根据部署环境修改

  window.UI_CONFIG = {
    lang: 'cn'
  }
})()

